/**
 * Production-ready EditorPageV3 - Refactored with modular architecture
 * Clean, maintainable, and scalable implementation
 */

import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useEditorV3, ChatMessage } from '../hooks/useEditorV3';
import ChatInterface from '../components/Editor/ChatInterface';
import PreviewPanel from '../components/Editor/PreviewPanel';
import PageManager from '../components/Editor/PageManager';
import IntentBasedEditor from '../components/IntentBasedEditor';

// ============================================================================
// TYPES
// ============================================================================

interface LocationState {
  prompt?: string;
  plan?: any;
  initialGeneration?: boolean;
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function EditorPageV3Refactored() {
  const location = useLocation();
  const navigate = useNavigate();
  const { prompt, plan, initialGeneration } = (location.state as LocationState) || {};

  // Use the custom hook for all editor functionality
  const { state, actions, refs } = useEditorV3();

  // Track if initial generation has been triggered to prevent duplicates
  const [hasTriggeredInitialGeneration, setHasTriggeredInitialGeneration] = useState(false);

  // Element selector state for ChatInterface
  const [elementSelectorActive, setElementSelectorActive] = useState(false);

  // Resizable panel dimensions
  const [pagesPanelWidth, setPagesPanelWidth] = useState(280);
  const [chatPanelWidth, setChatPanelWidth] = useState(280); // Reduced chat panel for more preview space
  const [pagesPanelCollapsed, setPagesPanelCollapsed] = useState(true);

  // Resize state
  const [isResizing, setIsResizing] = useState<'pages' | 'chat' | null>(null);

  // Resize handlers
  const handleMouseDown = (type: 'pages' | 'chat') => (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(type);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isResizing) return;

    if (isResizing === 'pages') {
      const newWidth = Math.max(200, Math.min(600, e.clientX));
      setPagesPanelWidth(newWidth);
    } else if (isResizing === 'chat') {
      const newWidth = Math.max(300, Math.min(600, window.innerWidth - e.clientX));
      setChatPanelWidth(newWidth);
    }
  };

  const handleMouseUp = () => {
    setIsResizing(null);
  };

  // Add global mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing]);

  // Additional state for robust UX
  const [showPageCreationDialog, setShowPageCreationDialog] = useState(false);
  const [pendingPageCreation, setPendingPageCreation] = useState<{
    pageName: string;
    pageId: string;
  } | null>(null);
  const [showLinkingDialog, setShowLinkingDialog] = useState(false);
  const [linkingProgress, setLinkingProgress] = useState<{
    current: number;
    total: number;
    currentPage: string;
  } | null>(null);
  const [isGeneratingIntent, setIsGeneratingIntent] = useState(false);

  // ============================================================================
  // INITIALIZATION EFFECTS
  // ============================================================================

  // Track if we've already shown the linking suggestion
  const [hasShownLinkingSuggestion, setHasShownLinkingSuggestion] = useState(false);

  // Simplified check for 2+ pages that need linking
  const checkIfPagesNeedLinking = (pages: any[]) => {
    const pagesWithContent = pages.filter(p => p.content && p.content.length > 50);
    // For simplicity: if we have 2+ pages, assume they need linking
    return pagesWithContent.length >= 2;
  };

  // Watch for page updates and auto-link when we have 2+ pages (with proper guards)
  useEffect(() => {
    const needsLinking = checkIfPagesNeedLinking(state.pages);

    // DISABLED: Auto-linking causes multiple popups and blinking
    // User can manually link pages using the Link Pages button
    if (false && needsLinking && !hasShownLinkingSuggestion && !showLinkingDialog && !linkingProgress) {
      console.log('🔗 Auto-linking disabled to prevent multiple popups');
      setHasShownLinkingSuggestion(true);
      handleLinkPages(); // Link immediately without dialog
    }
  }, [state.pages, hasShownLinkingSuggestion, showLinkingDialog, linkingProgress]);

  // Handle initial generation from plan page
  useEffect(() => {
    if (initialGeneration && prompt && !state.htmlContent && !hasTriggeredInitialGeneration) {
      console.log('🚀 Starting initial generation from plan page (ONCE)');
      console.log('📋 Plan data structure:', plan ? Object.keys(plan) : 'No plan');
      console.log('💬 Original prompt:', prompt);
      console.log('🔍 Plan sections:', plan?.sections?.length || 0);
      console.log('🔍 Plan features:', plan?.features?.length || 0);

      // Mark as triggered to prevent duplicates
      setHasTriggeredInitialGeneration(true);

      // Add user message
      const userMessage: ChatMessage = {
        role: 'user',
        content: prompt,
        timestamp: new Date()
      };
      actions.addMessage(userMessage);

      // Add plan message if we have a plan
      if (plan) {
        let planData;
        try {
          // Parse plan if it's a string
          planData = typeof plan === 'string' ? JSON.parse(plan) : plan;
        } catch (e) {
          planData = plan;
        }

        const planContent = formatPlanForDisplay(planData);
        const planMessage: ChatMessage = {
          role: 'assistant',
          content: planContent,
          timestamp: new Date(),
          type: 'plan'
        };
        actions.addMessage(planMessage);
      }

      // Create comprehensive prompt using BOTH original prompt AND plan data
      let comprehensivePrompt = prompt;

      if (plan && typeof plan === 'object') {
        // Build detailed prompt from plan data
        comprehensivePrompt = `${prompt}

📋 **DETAILED IMPLEMENTATION PLAN:**

🎯 **Project Overview:**
${plan.overview || 'Create a professional, modern design'}

🏗️ **Implementation Requirements:**`;

        // Add each section from the plan
        if (plan.sections && Array.isArray(plan.sections)) {
          plan.sections.forEach((section: any, index: number) => {
            comprehensivePrompt += `

${index + 1}. **${section.title}**
   ${section.description}`;

            if (section.details && Array.isArray(section.details)) {
              section.details.forEach((detail: string) => {
                comprehensivePrompt += `
   • ${detail}`;
              });
            }
          });
        }

        // Add features
        if (plan.features && Array.isArray(plan.features)) {
          comprehensivePrompt += `

✨ **Key Features to Implement:**`;
          plan.features.forEach((feature: string) => {
            comprehensivePrompt += `
• ${feature}`;
          });
        }

        // Add accessibility requirements
        if (plan.accessibility && Array.isArray(plan.accessibility)) {
          comprehensivePrompt += `

♿ **Accessibility Requirements:**`;
          plan.accessibility.forEach((item: string) => {
            comprehensivePrompt += `
• ${item}`;
          });
        }
      }

      // Add modal-ready instructions
      comprehensivePrompt += `

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for modal overlays
- Include proper structure for future enhancements`;

      console.log('🎯 Using comprehensive prompt with plan data:', comprehensivePrompt.substring(0, 200) + '...');
      console.log('📏 Final prompt length:', comprehensivePrompt.length);
      console.log('🔍 Plan sections included:', plan?.sections?.length || 0);
      console.log('🔍 Plan features included:', plan?.features?.length || 0);

      // Start generation with comprehensive prompt
      actions.generateFromPrompt(comprehensivePrompt);
    }
  }, [initialGeneration, prompt, state.htmlContent, plan, hasTriggeredInitialGeneration]);

  // Listen for navigation messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'NAVIGATE_TO_PAGE') {
        const { pageId, pageName } = event.data;
        console.log('🔗 Navigation message received:', { pageId, pageName });

        // Debug: Check if this is a known page name
        const existingPageNames = state.pages.map(p => p.name);
        console.log('🔗 Existing pages:', existingPageNames);
        console.log('🔗 Clicked page name:', pageName);

        // Handle navigation click
        handleNavigationClick({
          textContent: pageName,
          isNavigation: true
        });
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [state.pages]);

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================



  const handleChatSubmit = async (message: string) => {
    // Add user message
    const userMessage: ChatMessage = {
      role: 'user',
      content: message,
      timestamp: new Date()
    };
    actions.addMessage(userMessage);
    actions.clearInput();

    // Determine if this is an edit or new generation
    const hasContent = state.htmlContent.length > 0 || state.stableIframeContent.length > 0;

    if (hasContent) {
      // Always use the useEditorV3 editContent for chat messages
      // This will update state.htmlContent which will flow to IntentBasedEditor
      await actions.editContent(message);
    } else {
      await actions.generateFromPrompt(message);
    }
  };

  // Step 1: Generate Intent like Readdy.ai does
  const generateIntentFromElement = async (element: any) => {
    // DISABLED: Using new IntentBasedEditor component instead
    console.log('🔥 Old intent generation disabled - using new IntentBasedEditor system');
    return;

    // OLD CODE DISABLED TO PREVENT DUPLICATE CALLS
    // Prevent multiple intent calls for the same element
    if (element.intentGenerating || element.intentData || isGeneratingIntent) {
      console.log('🔥 Intent already generating or exists for this element');
      return;
    }

    // Mark this element as being processed
    element.intentGenerating = true;
    setIsGeneratingIntent(true);

    console.log('🔥 Starting intent generation for element:', element.textContent);
    const tagName = element.tagName?.toLowerCase() || 'div';
    const elementCode = element.outerHTML || `<${tagName}>${element.textContent || ''}</${tagName}>`;

    try {
      // Step 1: Call intent generation API (like Readdy's /api/page_gen/generate_intent)
      const response = await fetch('/api/llm/v3/generate-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          elementCode,
          htmlContent: state.htmlContent || state.stableIframeContent,
          conversationHistory: state.messages
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.code === 'OK' && result.data) {
          // Display the intent and suggestion like Readdy does
          actions.addMessage({
            role: 'assistant',
            content: result.data.userIntent,
            timestamp: new Date()
          });

          if (result.data.suggestion) {
            actions.addMessage({
              role: 'assistant',
              content: result.data.suggestion,
              timestamp: new Date()
            });
          }

          // Store the intent for later use in implementation
          element.intentData = result.data;
        }
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      // Fallback message
      actions.addMessage({
        role: 'assistant',
        content: `I understand you want to implement functionality for "${element.textContent}". Let me help you with that.`,
        timestamp: new Date()
      });
    } finally {
      element.intentGenerating = false;
      setIsGeneratingIntent(false);
    }
  };

  // Debounce element clicks to prevent rapid-fire calls
  const [lastClickTime, setLastClickTime] = useState(0);
  const CLICK_DEBOUNCE_MS = 1000; // 1 second debounce

  const handleElementClick = async (element: any) => {
    // DISABLED: Using new IntentBasedEditor component for element interactions
    console.log('🔥 Old element click handler disabled - using new IntentBasedEditor system');
    return;

    // OLD CODE DISABLED TO PREVENT CONFLICTS WITH NEW SYSTEM
    const now = Date.now();

    // Debounce rapid clicks on the same element
    if (now - lastClickTime < CLICK_DEBOUNCE_MS) {
      console.log('🔥 Click debounced, ignoring rapid click');
      return;
    }

    setLastClickTime(now);
    console.log('🔥 Element clicked in iframe:', element);

    // Handle navigation clicks
    if (element.isNavigation) {
      console.log('🔥 Navigation detected, handling navigation click');
      handleNavigationClick(element);
    } else if ((element.implementationType && element.implementationReason) || element.isInteractive) {
      // Handle interactive elements that need implementation
      console.log('🔥 Interactive element needs implementation:', element.implementationReason);

      // Step 1: Generate intent like Readdy does (only if not already generated)
      if (!element.intentData) {
        await generateIntentFromElement(element);
      }

      actions.setSelectedElement(element);
      actions.setShowImplementModal(true);
    } else {
      console.log('🔥 Element does not need implementation, ignoring');
    }
  };

  const handleNavigationClick = async (element: any) => {
    const pageName = element.textContent.trim();
    const pageId = generatePageId(pageName);

    console.log('🔥 Processing navigation click:', { pageName, pageId });

    // Smart page matching: handle both "Reports" and "Reports Page" scenarios
    const existingPage = state.pages.find(p => {
      const normalizedPageName = pageName.toLowerCase().trim();
      const normalizedExistingName = p.name.toLowerCase().trim();

      // Direct matches
      if (p.id === pageId || normalizedExistingName === normalizedPageName) {
        return true;
      }

      // Handle "Reports" → "Reports Page" mismatch
      if (normalizedExistingName === normalizedPageName + ' page') {
        return true;
      }

      // Handle "Reports Page" → "Reports" mismatch
      if (normalizedPageName === normalizedExistingName + ' page') {
        return true;
      }

      // Handle page ID matches (e.g., "reports" matches "reports-page")
      const clickedPageId = generatePageId(normalizedPageName);
      const existingPageId = generatePageId(normalizedExistingName);
      const clickedPageIdWithPage = generatePageId(normalizedPageName + ' page');

      if (p.id === clickedPageId || p.id === clickedPageIdWithPage || existingPageId === clickedPageId) {
        return true;
      }

      return false;
    });

    if (existingPage) {
      console.log('🔥 Page already exists, switching to:', existingPage);
      actions.switchToPage(existingPage.id);

      // Add feedback message
      actions.addMessage({
        role: 'assistant',
        content: `✅ Switched to "${existingPage.name}" page`,
        timestamp: new Date()
      });
    } else {
      console.log('🔥 Page does not exist, showing creation dialog');

      // Show confirmation dialog instead of creating immediately
      setPendingPageCreation({ pageName, pageId });
      setShowPageCreationDialog(true);
    }
  };

  const confirmPageCreation = async () => {
    if (!pendingPageCreation) {
      console.log('❌ No pending page creation');
      return;
    }

    const { pageName, pageId } = pendingPageCreation;
    console.log('🚀 Confirming page creation:', { pageName, pageId });

    // Close dialog
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    // Add feedback message
    actions.addMessage({
      role: 'assistant',
      content: `🚀 Creating new "${pageName}" page...`,
      timestamp: new Date()
    });

    // Create new page
    const newPage = {
      id: pageId,
      name: pageName,
      content: '',
      isActive: false
    };

    console.log('📄 Adding new page:', newPage);
    actions.addPage(newPage);

    console.log('🔄 Switching to new page:', pageId);
    actions.switchToPage(pageId);

    // Generate content for the new page
    const prompt = generatePagePrompt(pageName, state.pages);
    console.log('🎯 Generating content with prompt:', prompt.substring(0, 100) + '...');

    try {
      await actions.generateFromPrompt(prompt);
      console.log('✅ Page generation completed');

      // Auto-linking disabled to prevent multiple popups - user can manually link
      console.log('🔗 Page created successfully. Use Link Pages button to add navigation.');

    } catch (error) {
      console.error('❌ Page generation failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: `❌ Failed to generate content for "${pageName}". Please try again.`,
        timestamp: new Date()
      });
    }
  };

  const cancelPageCreation = () => {
    setShowPageCreationDialog(false);
    setPendingPageCreation(null);

    actions.addMessage({
      role: 'assistant',
      content: `❌ Page creation cancelled`,
      timestamp: new Date()
    });
  };

  const handlePageSwitch = (pageId: string) => {
    actions.switchToPage(pageId);

    const page = state.pages.find(p => p.id === pageId);
    if (page) {
      actions.addMessage({
        role: 'assistant',
        content: `📄 Switched to "${page.name}" page`,
        timestamp: new Date()
      });
    }
  };

  const handlePageAdd = (page: any) => {
    actions.addPage(page);
    actions.switchToPage(page.id);

    actions.addMessage({
      role: 'assistant',
      content: `📄 Created new page "${page.name}". Describe what content you'd like on this page.`,
      timestamp: new Date()
    });
  };

  const handleLinkPages = async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);

    if (pagesWithContent.length < 2) {
      actions.addMessage({
        role: 'assistant',
        content: '⚠️ Need at least 2 pages with content to link navigation',
        timestamp: new Date()
      });
      return;
    }

    // Prevent multiple linking operations
    if (linkingProgress) {
      console.log('🔗 Linking already in progress, ignoring duplicate call');
      return;
    }

    // Close linking dialog if open
    setShowLinkingDialog(false);

    // Initialize progress tracking
    setLinkingProgress({
      current: 0,
      total: pagesWithContent.length,
      currentPage: 'Starting...'
    });

    actions.addMessage({
      role: 'assistant',
      content: `🔗 Linking ${pagesWithContent.length} pages with navigation...`,
      timestamp: new Date()
    });

    try {
      // Use the improved linking with progress callback
      await linkPagesWithProgress(pagesWithContent);

      actions.addMessage({
        role: 'assistant',
        content: '✅ All pages have been linked with navigation!',
        timestamp: new Date()
      });

      // Reset linking suggestion state so it can show again for new pages
      setHasShownLinkingSuggestion(false);
    } catch (error) {
      console.error('Linking failed:', error);
      actions.addMessage({
        role: 'assistant',
        content: '❌ Failed to link some pages. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setLinkingProgress(null);
    }
  };

  // CLIENT-SIDE LINKING: Fast, immediate, no API calls needed
  const linkPagesWithProgress = async (pages: any[]) => {
    console.log(`🔗 Starting CLIENT-SIDE linking for ${pages.length} pages`);

    // Process all pages immediately on client-side
    pages.forEach((page, index) => {
      const otherPageNames = pages
        .filter(p => p.id !== page.id)
        .map(p => p.name);

      console.log(`🔗 Client-side linking page ${index + 1}/${pages.length}: ${page.name}`);

      // Update progress for this page
      setLinkingProgress({
        current: index + 1,
        total: pages.length,
        currentPage: page.name
      });

      try {
        // Parse the HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(page.content, 'text/html');

        // Find navigation area (try multiple selectors)
        let navElement = doc.querySelector('nav') ||
                        doc.querySelector('.nav') ||
                        doc.querySelector('.navigation') ||
                        doc.querySelector('header nav') ||
                        doc.querySelector('.header nav');

        if (!navElement) {
          // If no nav found, try to find header and add nav there
          const header = doc.querySelector('header') || doc.querySelector('.header');
          if (header) {
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            header.appendChild(navElement);
          } else {
            // Create a simple nav at the top of body
            navElement = doc.createElement('nav');
            navElement.className = 'navigation-links';
            navElement.style.cssText = 'padding: 1rem; background: #f8f9fa; border-bottom: 1px solid #dee2e6;';
            doc.body.insertBefore(navElement, doc.body.firstChild);
          }
        }

        // Clear existing auto-generated navigation links (but keep original nav content)
        const existingAutoLinks = navElement.querySelectorAll('a[data-page-link="true"]');
        existingAutoLinks.forEach(link => link.remove());

        // Also remove any links that match other page names (to prevent duplicates)
        const allLinks = navElement.querySelectorAll('a');
        allLinks.forEach(link => {
          const linkText = link.textContent?.trim().toLowerCase();
          const isPageLink = otherPageNames.some(pageName => {
            const normalizedPageName = pageName.toLowerCase();
            return linkText === normalizedPageName ||
                   linkText === (normalizedPageName + ' page') ||
                   linkText === normalizedPageName.replace(' page', '') ||
                   (linkText + ' page') === normalizedPageName;
          });
          if (isPageLink) {
            link.remove();
          }
        });

        // Add links to other pages with proper navigation attributes
        otherPageNames.forEach(pageName => {
          const link = doc.createElement('a');
          link.href = '#';

          // Use the clean page name for display (remove " Page" suffix if present)
          const displayName = pageName.endsWith(' Page') ? pageName.replace(' Page', '') : pageName;
          link.textContent = displayName;

          // Use data-nav attribute for proper navigation (this is what the system expects)
          const pageId = pageName.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .replace(/^-+|-+$/g, '');

          link.setAttribute('data-nav', pageId);
          link.setAttribute('data-page-link', 'true'); // For our tracking
          link.style.cssText = 'margin-right: 1rem; color: #007bff; text-decoration: none; cursor: pointer;';

          navElement.appendChild(link);
        });

        // Ensure the page has proper SPA router script for navigation
        let scriptElement = doc.querySelector('script[data-exec="inline"]');
        if (!scriptElement) {
          scriptElement = doc.createElement('script');
          scriptElement.setAttribute('data-exec', 'inline');
          scriptElement.textContent = `
// SPA Router for page navigation
document.addEventListener('click', (e) => {
  const target = e.target.closest('[data-nav]');
  if (target) {
    e.preventDefault();
    const pageId = target.getAttribute('data-nav');
    const pageName = target.textContent.trim();

    console.log('🔗 Navigation click in iframe:', {
      pageId,
      pageName,
      targetElement: target.outerHTML
    });

    // Send message to parent to switch pages
    if (window.parent && window.parent.postMessage) {
      window.parent.postMessage({
        type: 'NAVIGATE_TO_PAGE',
        pageId: pageId,
        pageName: pageName
      }, '*');
    }
  }
});
          `;
          doc.body.appendChild(scriptElement);
        }

        // Update the page content with modified HTML
        const updatedHtml = doc.documentElement.outerHTML;
        actions.updatePage(page.id, { content: updatedHtml });
        console.log(`✅ Client-side linking completed for ${page.name}`);

      } catch (error) {
        console.error(`❌ Failed to update page ${page.name}:`, error);
      }
    });

    // No need to wait for API calls - everything is done immediately!
    console.log('🔗 All pages linked instantly via client-side manipulation');
  };

  const extractHtmlFromResponse = (response: string): string => {
    if (!response) return '';

    // Look for HTML content between ```html and ``` markers
    const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
    if (htmlMatch) {
      return htmlMatch[1].trim();
    }

    // Look for HTML starting with DOCTYPE or html tag
    const doctypeMatch = response.match(/(<!DOCTYPE html[\s\S]*)/i);
    if (doctypeMatch) {
      return doctypeMatch[1].trim();
    }

    const htmlTagMatch = response.match(/(<html[\s\S]*)/i);
    if (htmlTagMatch) {
      return htmlTagMatch[1].trim();
    }

    // If response contains HTML tags, assume it's HTML
    if (response.includes('<') && response.includes('>')) {
      const firstTagIndex = response.indexOf('<');
      return response.substring(firstTagIndex).trim();
    }

    return response;
  };

  const handleImplementChoice = async (choice: 'inline' | 'modal' | 'page') => {
    if (!state.selectedElement) return;

    // Prevent multiple implementation calls
    if (state.isGenerating) {
      console.log('🔥 Implementation already in progress, ignoring');
      return;
    }

    actions.setShowImplementModal(false);

    const elementText = state.selectedElement.textContent;
    const elementType = state.selectedElement.implementationType || 'interactive';

    // Add user message showing their choice
    actions.addMessage({
      role: 'user',
      content: `Implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : choice === 'modal' ? 'a modal/popup' : 'a new page'}`,
      timestamp: new Date()
    });

    if (choice === 'page') {
      // Handle page creation
      const pageName = elementText.includes('Sign') || elementText.includes('Login') || elementText.includes('Register')
        ? elementText
        : `${elementText} Page`;
      const pageId = generatePageId(pageName);

      // Check if page already exists
      const existingPage = state.pages.find(p => p.id === pageId);
      if (existingPage) {
        actions.switchToPage(pageId);
        actions.addMessage({
          role: 'assistant',
          content: `✅ Switched to existing "${pageName}" page`,
          timestamp: new Date()
        });
        return;
      }

      // Create new page
      const newPage = {
        id: pageId,
        name: pageName,
        content: '',
        isActive: false
      };

      actions.addPage(newPage);
      actions.switchToPage(pageId);

      // Generate content for the new page
      await actions.generateFromPrompt(`Create a ${pageName} page`);

      // Auto-linking disabled to prevent multiple popups - user can manually link
      console.log('🔗 Element-based page created successfully. Use Link Pages button to add navigation.');
    } else {
      // Handle inline or modal implementation using editContent (includes conversation history)
      try {
        // Create intent and user messages to include in conversation history
        const additionalMessages: ChatMessage[] = [];

        // Add intent message if available
        if (state.selectedElement.intentData) {
          additionalMessages.push({
            role: 'assistant',
            content: `🎯 **Intent Analysis:**\n${state.selectedElement.intentData.userIntent}\n\n💡 **Suggestion:**\n${state.selectedElement.intentData.suggestion || "I can help implement this functionality."}`,
            timestamp: new Date()
          });
        }

        // Add user message
        const userMessage: ChatMessage = {
          role: 'user',
          content: `Implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}`,
          timestamp: new Date()
        };
        additionalMessages.push(userMessage);

        // Add messages to chat UI
        additionalMessages.forEach(msg => actions.addMessage(msg));

        // Create implementation prompt that references the intent analysis
        const intentContext = state.selectedElement.intentData
          ? `Based on the intent analysis above: "${state.selectedElement.intentData.userIntent}"

${state.selectedElement.intentData.suggestion || "Please implement this functionality."}

` : '';

        const implementationPrompt = `${intentContext}Please implement "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}.

Context: User clicked on an element and wants to implement functionality. Please modify the existing content to add this feature while preserving the current design and layout.

Implementation type: ${choice}
Element type: ${elementType}

${choice === 'modal' ? 'Create a modal/popup that opens when the element is clicked.' : 'Add the functionality directly to the current page.'}

Important: The intent analysis and suggestion above should guide your implementation approach.`;

        // Use editContent with explicit conversation history including intent
        await actions.editContent(implementationPrompt, additionalMessages);

        actions.addMessage({
          role: 'assistant',
          content: `✅ Successfully implemented "${elementText}" as ${choice === 'inline' ? 'inline functionality' : 'a modal/popup'}!`,
          timestamp: new Date()
        });

      } catch (error) {
        console.error('Implementation error:', error);
        actions.addMessage({
          role: 'assistant',
          content: `❌ Failed to implement "${elementText}". Please try again.`,
          timestamp: new Date()
        });
      }
    }
  };

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const formatPlanForDisplay = (plan: any): string => {
    if (typeof plan === 'string') {
      return plan;
    }

    if (plan && typeof plan === 'object') {
      let planContent = '';

      // Add overview
      if (plan.overview) {
        planContent += `${plan.overview}\n\n`;
      }

      // Add sections
      if (plan.sections && Array.isArray(plan.sections)) {
        plan.sections.forEach((section: any, index: number) => {
          planContent += `${index + 1}. ${section.title}\n`;
          if (section.description) {
            planContent += `${section.description}\n`;
          }
          if (section.details && Array.isArray(section.details)) {
            section.details.forEach((detail: string) => {
              planContent += `• ${detail}\n`;
            });
          }
          planContent += '\n';
        });
      }

      // Add features
      if (plan.features && Array.isArray(plan.features)) {
        planContent += `Key Features:\n`;
        plan.features.forEach((feature: string) => {
          planContent += `• ${feature}\n`;
        });
        planContent += '\n';
      }

      // Add accessibility
      if (plan.accessibility && Array.isArray(plan.accessibility)) {
        planContent += `Accessibility:\n`;
        plan.accessibility.forEach((item: string) => {
          planContent += `• ${item}\n`;
        });
      }

      return planContent.trim();
    }

    return '';
  };

  const generatePageId = (name: string): string => {
    return name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  const generatePagePrompt = (pageName: string, existingPages: any[]): string => {
    const existingPageNames = existingPages
      .filter(p => p.name !== 'Main Page')
      .map(p => p.name);

    return `Create a complete, production-grade ${pageName} page with:

🎯 **CRITICAL REQUIREMENTS:**
1. **Full Navigation Bar** - Include links to ALL existing pages: Home/Main Page${existingPageNames.length > 0 ? ', ' + existingPageNames.join(', ') : ''}
2. **Consistent Design** - Match the exact style, colors, and layout of the main page
3. **Professional Content** - High-quality, relevant content for "${pageName}"
4. **Responsive Layout** - Mobile-friendly design
5. **Interactive Elements** - Buttons, forms, or features appropriate for this page

🔗 **Navigation Requirements:**
- Navigation bar at the top with links to: Home, ${existingPageNames.join(', ')}${existingPageNames.length > 0 ? ', ' : ''}About, Contact
- Each nav link should be clickable (use <a> tags with proper text)
- Current page ("${pageName}") should be highlighted/active in navigation
- Consistent navigation styling across all pages

📄 **Content for ${pageName}:**
${getPageContentGuidelines(pageName)}

Create a complete, standalone HTML page that looks professional and matches the main page design exactly.`;
  };

  const getPageContentGuidelines = (pageName: string): string => {
    const name = pageName.toLowerCase();

    if (name.includes('about')) {
      return '- Company/team information, mission, values, history, team members';
    } else if (name.includes('contact')) {
      return '- Contact form, address, phone, email, social media links, map';
    } else if (name.includes('services')) {
      return '- Service offerings, pricing, features, benefits';
    } else if (name.includes('sign up') || name.includes('signup') || name.includes('register')) {
      return '- Registration form with fields like name, email, password, confirm password\n- Clear call-to-action buttons\n- Terms of service and privacy policy links\n- Social login options if appropriate\n- "Already have an account? Login" link';
    } else if (name.includes('login') || name.includes('sign in') || name.includes('signin')) {
      return '- Login form with email/username and password fields\n- "Remember me" checkbox\n- "Forgot password?" link\n- "Don\'t have an account? Sign up" link\n- Social login options if appropriate';
    } else if (name.includes('pricing')) {
      return '- Pricing tiers with features comparison\n- Monthly/yearly toggle\n- "Most popular" highlighting\n- Clear call-to-action buttons\n- FAQ section about pricing';
    } else {
      return `- Relevant, professional content appropriate for "${pageName}"`;
    }
  };

  // ============================================================================
  // RENDER
  // ============================================================================

  return (
    <div className="h-screen bg-gray-50 flex w-full">
      {/* Resizable Pages Panel */}
      {!pagesPanelCollapsed && (
        <>
          <div
            className="flex-shrink-0 bg-white border-r border-gray-200 relative"
            style={{ width: pagesPanelWidth }}
          >
            <PageManager
              pages={state.pages}
              currentPageId={state.currentPageId}
              isLinking={!!linkingProgress}
              onPageSwitch={handlePageSwitch}
              onPageAdd={handlePageAdd}
              onPageUpdate={actions.updatePage}
              onLinkPages={() => {
                setShowLinkingDialog(true);
              }}
            />
            {/* Collapse button */}
            <button
              onClick={() => setPagesPanelCollapsed(true)}
              className="absolute top-4 right-4 p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors z-10"
              title="Collapse Pages Panel"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          </div>
          {/* Pages Resize Handle */}
          <div
            className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
            onMouseDown={handleMouseDown('pages')}
            title="Drag to resize pages panel"
          />
        </>
      )}

      {/* Collapsed Pages Toggle */}
      {pagesPanelCollapsed && (
        <div className="w-12 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col items-center py-4">
          <button
            onClick={() => setPagesPanelCollapsed(false)}
            className="p-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-colors"
            title="Expand Pages Panel"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
          <div className="mt-4 text-xs text-gray-400 transform -rotate-90 whitespace-nowrap">
            Pages
          </div>
        </div>
      )}

      {/* Main Content Area */}
      <div className="flex-1 flex w-full">
        {/* Intent-Based Editor */}
        <div className="flex-1 w-full">
          <IntentBasedEditor
            prototypeId={1} // TODO: Get from context or props
            userId={1} // TODO: Get from auth context
            initialHtml={state.htmlContent || state.stableIframeContent || ''}
            pageUrl={window.location.href}
            isCreateMode={initialGeneration || !location.pathname.includes('/editor/')} // Use create mode for new prototypes
            elementSelectorActive={elementSelectorActive}
            isGenerating={state.isGenerating} // Pass generation state for progress indicators
            streamingContent={state.streamingContent} // Pass streaming content for real-time updates
            onElementSelected={(element) => {
              // When element is selected, show the main implementation modal
              actions.setSelectedElement(element);
              actions.setShowImplementModal(true);
              setElementSelectorActive(false); // Turn off selector after selection
            }}
            onHtmlChange={(html) => {
              // Update both content states when IntentBasedEditor changes HTML
              actions.setHtmlContent(html);
              actions.setStableIframeContent(html);

              // Update current page content
              actions.updatePage(state.currentPageId, { content: html });
            }}
            onError={(error) => {
              actions.addMessage({
                role: 'assistant',
                content: `❌ Error: ${error}`,
                timestamp: new Date()
              });
            }}
            onClose={() => {
              // Navigate back to My Prototypes or previous page
              navigate('/my-prototypes');
            }}
            onAddChatMessage={(message) => {
              // Add intent messages to chat
              actions.addMessage(message);
            }}
            onEditContent={async (prompt, additionalMessages) => {
              // Use editContent for intent-based implementations
              await actions.editContent(prompt, additionalMessages);
            }}
            onIntentGenerationChange={(generating) => {
              // Update intent generation state for progress indicator
              setIsGeneratingIntent(generating);
            }}
          />
        </div>

        {/* Chat Resize Handle */}
        <div
          className="w-1 bg-gray-200 hover:bg-blue-400 cursor-col-resize transition-colors flex-shrink-0"
          onMouseDown={handleMouseDown('chat')}
          title="Drag to resize chat panel"
        />

        {/* Resizable Chat Interface */}
        <div
          className="flex-shrink-0 bg-white border-l border-gray-200"
          style={{ width: chatPanelWidth }}
        >
          <ChatInterface
            messages={state.messages}
            input={state.input}
            isGenerating={state.isGenerating}
            onInputChange={actions.setInput}
            onSubmit={handleChatSubmit}
            onToggleSelector={() => {
              setElementSelectorActive(!elementSelectorActive);
            }}
            isSelectorActive={elementSelectorActive}
          />
        </div>
      </div>

      {/* Page Creation Confirmation Dialog */}
      {showPageCreationDialog && pendingPageCreation && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Create New Page</h3>
            </div>

            <p className="text-gray-600 mb-2">
              You clicked on a navigation link for <strong>"{pendingPageCreation.pageName}"</strong>
            </p>
            <p className="text-sm text-gray-500 mb-6">
              This page doesn't exist yet. Would you like me to create it with relevant content?
            </p>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-blue-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Create a new "{pendingPageCreation.pageName}" page</li>
                <li>• Generate relevant content for this page type</li>
                <li>• Maintain consistent design with your existing pages</li>
                <li>• Add proper navigation links</li>
              </ul>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={cancelPageCreation}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  console.log('🔘 Create Page button clicked');
                  confirmPageCreation();
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create Page
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Linking Suggestion Dialog */}
      {showLinkingDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Link Your Pages</h3>
            </div>

            <p className="text-gray-600 mb-2">
              Great! You now have multiple pages.
            </p>
            <p className="text-sm text-gray-500 mb-6">
              Would you like me to update the navigation on all pages so visitors can easily navigate between them?
            </p>

            <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-6">
              <p className="text-sm text-green-800">
                <strong>What I'll do:</strong>
              </p>
              <ul className="text-sm text-green-700 mt-1 space-y-1">
                <li>• Update navigation bars on all pages</li>
                <li>• Add links to all your pages</li>
                <li>• Maintain consistent styling</li>
                <li>• Preserve all existing content</li>
              </ul>
            </div>

            <div className="flex justify-between items-center">
              <button
                onClick={() => {
                  setShowLinkingDialog(false);
                  setHasShownLinkingSuggestion(true); // Prevent showing again
                }}
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Don't show again
              </button>

              <div className="flex space-x-3">
                <button
                  onClick={() => setShowLinkingDialog(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Maybe Later
                </button>
                <button
                  onClick={handleLinkPages}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.102m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                  Link Pages
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Linking Progress Modal */}
      {linkingProgress && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-blue-600 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Linking Pages</h3>
            </div>

            <p className="text-gray-600 mb-4">
              Updating navigation on all pages...
            </p>

            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Progress</span>
                <span>{linkingProgress.current} of {linkingProgress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(linkingProgress.current / linkingProgress.total) * 100}%` }}
                ></div>
              </div>
            </div>

            {linkingProgress.currentPage && (
              <p className="text-sm text-gray-500">
                Currently updating: <strong>{linkingProgress.currentPage}</strong>
              </p>
            )}
          </div>
        </div>
      )}

      {/* Implementation Choice Modal */}
      {state.showImplementModal && state.selectedElement && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-lg w-full mx-4">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold text-gray-900">Implement Feature</h3>
                <button
                  onClick={() => actions.setShowImplementModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="mb-6">
                <div className="flex items-center mb-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">"{state.selectedElement.textContent}"</p>
                    <p className="text-sm text-gray-500">{state.selectedElement.implementationReason}</p>
                  </div>
                </div>

                {/* Intent Generation Progress */}
                {isGeneratingIntent && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center">
                      <div className="w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-3"></div>
                      <div>
                        <p className="text-sm font-medium text-blue-900">Analyzing element...</p>
                        <p className="text-xs text-blue-700">Understanding what you want to implement</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-900 mb-3">How would you like to implement this?</h4>
                <div className="space-y-3">
                  {/* Inline Implementation */}
                  <button
                    onClick={() => handleImplementChoice('inline')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200">
                        <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Modify Current Page</h5>
                        <p className="text-sm text-gray-500">Add functionality directly to this page</p>
                      </div>
                    </div>
                  </button>

                  {/* Modal Implementation */}
                  <button
                    onClick={() => handleImplementChoice('modal')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200">
                        <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create Modal/Popup</h5>
                        <p className="text-sm text-gray-500">Open content in an overlay window</p>
                      </div>
                    </div>
                  </button>

                  {/* New Page Implementation */}
                  <button
                    onClick={() => handleImplementChoice('page')}
                    className="w-full p-4 text-left border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50 transition-colors group"
                  >
                    <div className="flex items-start">
                      <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200">
                        <svg className="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-900">Create New Page</h5>
                        <p className="text-sm text-gray-500">Navigate to a dedicated page for this feature</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              <div className="flex justify-end">
                <button
                  onClick={() => actions.setShowImplementModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default EditorPageV3Refactored;
